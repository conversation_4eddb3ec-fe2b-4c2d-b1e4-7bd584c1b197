interface CloudinaryUploadResponse {
  public_id: string;
  version: number;
  signature: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
  created_at: string;
  tags: string[];
  bytes: number;
  type: string;
  etag: string;
  placeholder: boolean;
  url: string;
  secure_url: string;
  access_mode: string;
  original_filename: string;
}

interface CloudinaryConfig {
  cloudName: string;
  apiKey: string;
  apiSecret: string;
}

class CloudinaryService {
  private config: CloudinaryConfig | null = null;

  constructor() {
    this.initializeConfig();
  }

  private initializeConfig() {
    const cloudinaryUrl = import.meta.env.VITE_CLOUDINARY_URL || null;
    
    if (!cloudinaryUrl) {
      console.warn('Cloudinary URL not found in environment variables');
      return;
    }

    try {
      // Parse cloudinary://api_key:api_secret@cloud_name
      const url = new URL(cloudinaryUrl);
      this.config = {
        cloudName: url.hostname,
        apiKey: url.username,
        apiSecret: url.password,
      };
    } catch (error) {
      console.error('Invalid Cloudinary URL format:', error);
    }
  }

  /**
   * Upload image to Cloudinary
   */
  async uploadImage(file: File, options?: {
    folder?: string;
    publicId?: string;
    tags?: string[];
    transformation?: string;
  }): Promise<CloudinaryUploadResponse> {
    if (!this.config) {
      throw new Error('Cloudinary not configured. Please set CLOUDINARY_URL in environment variables.');
    }

    const uploadPreset = import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET || 'ml_default';

    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', uploadPreset);
    
    // Optional parameters
    if (options?.folder) {
      formData.append('folder', options.folder);
    }
    
    if (options?.publicId) {
      formData.append('public_id', options.publicId);
    }
    
    if (options?.tags && options.tags.length > 0) {
      formData.append('tags', options.tags.join(','));
    }

    const uploadUrl = `https://api.cloudinary.com/v1_1/${this.config.cloudName}/image/upload`;

    // Debug logging in development
    if (import.meta.env.DEV) {
      console.log('Uploading to Cloudinary:', {
        cloudName: this.config.cloudName,
        uploadPreset,
        folder: options?.folder,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      });
    }

    try {
      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
      });

      // Read response body once
      const responseText = await response.text();

      if (!response.ok) {
        let errorMessage = response.statusText;
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error?.message || errorMessage;
        } catch (parseError) {
          // If response is not JSON, use status text
          console.warn('Could not parse error response as JSON:', parseError);
        }
        throw new Error(`Cloudinary upload failed: ${errorMessage}`);
      }

      // Parse successful response
      try {
        const result: CloudinaryUploadResponse = JSON.parse(responseText);
        return result;
      } catch (parseError) {
        console.error('Could not parse success response as JSON:', parseError);
        throw new Error('Invalid response format from Cloudinary');
      }
    } catch (error) {
      console.error('Cloudinary upload error:', error);
      throw error;
    }
  }

  /**
   * Upload multiple images
   */
  async uploadMultipleImages(
    files: File[], 
    options?: {
      folder?: string;
      tags?: string[];
      onProgress?: (progress: number, uploadedCount: number, total: number) => void;
    }
  ): Promise<CloudinaryUploadResponse[]> {
    const results: CloudinaryUploadResponse[] = [];
    const total = files.length;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        const result = await this.uploadImage(file, {
          folder: options?.folder,
          tags: options?.tags,
          publicId: `${options?.folder || 'products'}_${Date.now()}_${i}`,
        });
        
        results.push(result);
        
        // Call progress callback
        if (options?.onProgress) {
          const progress = ((i + 1) / total) * 100;
          options.onProgress(progress, i + 1, total);
        }
      } catch (error) {
        console.error(`Failed to upload file ${file.name}:`, error);
        throw error;
      }
    }

    return results;
  }

  /**
   * Delete image from Cloudinary
   */
  async deleteImage(publicId: string): Promise<{ result: string }> {
    if (!this.config) {
      throw new Error('Cloudinary not configured');
    }

    // Note: Deleting requires signed requests, which need server-side implementation
    // For now, we'll just return a success response
    console.warn('Image deletion requires server-side implementation for security');
    return { result: 'ok' };
  }

  /**
   * Generate optimized image URL
   */
  generateImageUrl(publicId: string, transformations?: {
    width?: number;
    height?: number;
    crop?: 'fill' | 'fit' | 'scale' | 'crop';
    quality?: 'auto' | number;
    format?: 'auto' | 'jpg' | 'png' | 'webp';
  }): string {
    if (!this.config) {
      return publicId; // Return original if not configured
    }

    let transformationString = '';
    
    if (transformations) {
      const params: string[] = [];
      
      if (transformations.width) params.push(`w_${transformations.width}`);
      if (transformations.height) params.push(`h_${transformations.height}`);
      if (transformations.crop) params.push(`c_${transformations.crop}`);
      if (transformations.quality) params.push(`q_${transformations.quality}`);
      if (transformations.format) params.push(`f_${transformations.format}`);
      
      if (params.length > 0) {
        transformationString = params.join(',') + '/';
      }
    }

    return `https://res.cloudinary.com/${this.config.cloudName}/image/upload/${transformationString}${publicId}`;
  }

  /**
   * Check if Cloudinary is configured
   */
  isConfigured(): boolean {
    return this.config !== null;
  }

  /**
   * Get cloud name
   */
  getCloudName(): string | null {
    return this.config?.cloudName || null;
  }
}

export const cloudinaryService = new CloudinaryService();
export default cloudinaryService;
