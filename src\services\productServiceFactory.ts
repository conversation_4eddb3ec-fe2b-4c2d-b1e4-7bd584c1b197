import { APP_CONFIG } from '../config/app';
import { productApiService } from './productApiService';
import productService from './productService';
import type { 
  Product, 
  ProductFormData, 
  ProductListParams, 
  ProductListResponse 
} from '../types/product';
import type { ApiResponse } from '../types/api';

// Helper function to convert old image format to new format
function convertImageUrls(imageString?: string): string[] {
  if (!imageString) return [];
  return imageString.split(',')
    .map((img: string) => img.trim())
    .filter((img: string) => img.length > 0);
}

// Interface that both services should implement
interface IProductService {
  getAllProducts(params?: ProductListParams): Promise<Product[] | ProductListResponse>;
  getProductById(id: string | number): Promise<Product | null>;
  createProduct(productData: ProductFormData): Promise<Product>;
  updateProduct(id: string | number, productData: ProductFormData): Promise<Product>;
  deleteProduct(id: string | number): Promise<boolean>;
  searchProducts(query: string, params?: Omit<ProductListParams, 'search'>): Promise<Product[] | ProductListResponse>;
}

// Adapter for the old mock service to match the new interface
class MockProductServiceAdapter implements IProductService {
  async getAllProducts(params?: ProductListParams): Promise<Product[]> {
    // Get products from old service
    let products = await productService.getAllProducts();

    // Apply search filter if provided
    if (params?.search) {
      products = await productService.searchProducts(params.search);
    }

    // Convert old format to new format
    const convertedProducts = products.map(oldProduct => ({
      id: parseInt(oldProduct.id || '0'),
      name: (oldProduct as any).product_name,
      price: (oldProduct as any).price,
      imageUrls: convertImageUrls((oldProduct as any).image),
      ingredients: oldProduct.ingredients,
      instructions: oldProduct.instructions,
      benefits: oldProduct.benefits,
      usage: oldProduct.usage,
      notes: (oldProduct as any).notes,
      otherInfo: oldProduct.otherInfo,
      targetUser: oldProduct.targetUser,
      purpose: oldProduct.purpose,
      description: oldProduct.description,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(), // Random date within last 30 days
      updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(), // Random date within last 7 days
      faqs: oldProduct.faqs || []
    }));

    // Apply sorting if provided
    let sortedProducts = [...convertedProducts];
    if (params?.sortBy) {
      sortedProducts.sort((a, b) => {
        const aValue = (a as any)[params.sortBy!];
        const bValue = (b as any)[params.sortBy!];

        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        if (aValue > bValue) comparison = 1;

        return params.sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    // Apply pagination if provided
    if (params?.page && params?.limit) {
      const startIndex = (params.page - 1) * params.limit;
      const endIndex = startIndex + params.limit;
      return sortedProducts.slice(startIndex, endIndex);
    }

    return sortedProducts;
  }

  async getProductById(id: string | number): Promise<Product | null> {
    const oldProduct = await productService.getProductById(id.toString());
    if (!oldProduct) return null;

    // Convert old format to new format
    return {
      id: parseInt(oldProduct.id || '0'),
      name: (oldProduct as any).product_name,
      price: (oldProduct as any).price,
      imageUrls: convertImageUrls((oldProduct as any).image),
      ingredients: oldProduct.ingredients,
      instructions: oldProduct.instructions,
      benefits: oldProduct.benefits,
      usage: oldProduct.usage,
      notes: (oldProduct as any).notes,
      otherInfo: oldProduct.otherInfo,
      targetUser: oldProduct.targetUser,
      purpose: oldProduct.purpose,
      description: oldProduct.description,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      faqs: oldProduct.faqs || []
    };
  }

  async createProduct(productData: ProductFormData): Promise<Product> {
    // Convert new format to old format
    const oldFormatData = {
      ...productData,
      product_name: productData.product_name,
      notes: productData.description || '',
      image: productData.images?.join(', ') || '',
      price: typeof productData.price === 'number' ? `${productData.price}$` : productData.price.toString(),
    };
    
    return await productService.createProduct(oldFormatData as any);
  }

  async updateProduct(id: string | number, productData: ProductFormData): Promise<Product> {
    // Convert new format to old format
    const oldFormatData = {
      ...productData,
      product_name: productData.product_name,
      notes: productData.description || '',
      image: productData.images?.join(', ') || '',
      price: typeof productData.price === 'number' ? `${productData.price}$` : productData.price.toString(),
    };
    
    return await productService.updateProduct(id.toString(), oldFormatData as any);
  }

  async deleteProduct(id: string | number): Promise<boolean> {
    return await productService.deleteProduct(id.toString());
  }

  async searchProducts(query: string, params?: Omit<ProductListParams, 'search'>): Promise<Product[]> {
    const products = await productService.searchProducts(query);

    // Convert old format to new format
    return products.map(oldProduct => ({
      id: parseInt(oldProduct.id || '0'),
      name: (oldProduct as any).product_name,
      price: (oldProduct as any).price,
      imageUrls: convertImageUrls((oldProduct as any).image),
      ingredients: oldProduct.ingredients,
      instructions: oldProduct.instructions,
      benefits: oldProduct.benefits,
      usage: oldProduct.usage,
      notes: (oldProduct as any).notes,
      otherInfo: oldProduct.otherInfo,
      targetUser: oldProduct.targetUser,
      purpose: oldProduct.purpose,
      description: oldProduct.description,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      faqs: oldProduct.faqs || []
    }));
  }
}

// Adapter for the new API service
class ApiProductServiceAdapter implements IProductService {
  async getAllProducts(params?: ProductListParams): Promise<ProductListResponse> {
    return await productApiService.getAllProducts(params);
  }

  async getProductById(id: string | number): Promise<Product | null> {
    try {
      const response = await productApiService.getProductById(id);
      return response.success ? response.data : null;
    } catch (error) {
      console.error('Error getting product by ID:', error);
      return null;
    }
  }

  async createProduct(productData: ProductFormData): Promise<Product> {
    const response = await productApiService.createProduct(productData);
    if (!response.success) {
      throw new Error(response.message || 'Failed to create product');
    }
    return response.data;
  }

  async updateProduct(id: string | number, productData: ProductFormData): Promise<Product> {
    const response = await productApiService.updateProduct(id, productData);
    if (!response.success) {
      throw new Error(response.message || 'Failed to update product');
    }
    return response.data;
  }

  async deleteProduct(id: string | number): Promise<boolean> {
    try {
      const response = await productApiService.deleteProduct(id);
      return response.success;
    } catch (error) {
      console.error('Error deleting product:', error);
      return false;
    }
  }

  async searchProducts(query: string, params?: Omit<ProductListParams, 'search'>): Promise<ProductListResponse> {
    return await productApiService.searchProducts(query, params);
  }
}

// Factory function to get the appropriate service
export function createProductService(): IProductService {
  if (APP_CONFIG.USE_MOCK_API || !APP_CONFIG.ENABLE_PRODUCT_API) {
    return new MockProductServiceAdapter();
  } else {
    return new ApiProductServiceAdapter();
  }
}

// Export the service instance
export const productServiceInstance = createProductService();
export default productServiceInstance;
