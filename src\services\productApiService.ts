import { apiClient } from './apiClient';
import { API_ENDPOINTS } from '../constants/api';
import type { 
  Product, 
  ProductFormData, 
  ProductListParams, 
  ProductListResponse,
  BulkImportRequest,
  BulkImportResponse
} from '../types/product';
import type { ApiResponse } from '../types/api';

class ProductApiService {
  /**
   * Get all products with pagination and filtering
   */
  async getAllProducts(params?: ProductListParams): Promise<ProductListResponse> {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const url = `${API_ENDPOINTS.PRODUCTS.LIST}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiClient.get<Product[]>(url);

    // The API returns the response in the format expected by ProductListResponse
    return response as any as ProductListResponse;
  }

  /**
   * Get a single product by ID
   */
  async getProductById(id: string | number): Promise<ApiResponse<Product>> {
    return await apiClient.get<Product>(API_ENDPOINTS.PRODUCTS.GET(id.toString()));
  }

  /**
   * Create a new product
   */
  async createProduct(productData: ProductFormData): Promise<ApiResponse<Product>> {
    return await apiClient.post<Product>(API_ENDPOINTS.PRODUCTS.CREATE, productData);
  }

  /**
   * Update a product (full update)
   */
  async updateProduct(id: string | number, productData: ProductFormData): Promise<ApiResponse<Product>> {
    return await apiClient.put<Product>(API_ENDPOINTS.PRODUCTS.UPDATE(id.toString()), productData);
  }

  /**
   * Partially update a product
   */
  async patchProduct(id: string | number, productData: Partial<ProductFormData>): Promise<ApiResponse<Product>> {
    return await apiClient.patch<Product>(API_ENDPOINTS.PRODUCTS.PATCH(id.toString()), productData);
  }

  /**
   * Delete a product
   */
  async deleteProduct(id: string | number): Promise<ApiResponse<{ message: string }>> {
    return await apiClient.delete<{ message: string }>(API_ENDPOINTS.PRODUCTS.DELETE(id.toString()));
  }

  /**
   * Bulk import products
   */
  async bulkImportProducts(data: BulkImportRequest): Promise<BulkImportResponse> {
    return await apiClient.post<BulkImportResponse>(API_ENDPOINTS.PRODUCTS.BULK_IMPORT, data);
  }

  /**
   * Search products (using the list endpoint with search parameter)
   */
  async searchProducts(query: string, params?: Omit<ProductListParams, 'search'>): Promise<ProductListResponse> {
    return this.getAllProducts({ ...params, search: query });
  }
}

export const productApiService = new ProductApiService();
export default productApiService;
