import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  message,
  Row,
  Col,
  Space,
  Divider,
  Modal,
  Spin,
  InputNumber,
  Upload,
  Image
} from 'antd';
import { ArrowLeftOutlined, PlusOutlined, DeleteOutlined, UploadOutlined, EyeOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { ProductFormData, FAQ } from '../../types/product';
import { useProduct } from '../../hooks/useProduct';
import ImageUpload from '../../components/common/ImageUpload';

const { Title } = Typography;
const { TextArea } = Input;

const ProductEdit: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [faqModalVisible, setFaqModalVisible] = useState(false);
  const [faqForm] = Form.useForm();
  const [editingFaqIndex, setEditingFaqIndex] = useState<number | null>(null);
  const [imageList, setImageList] = useState<string[]>([]);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const { product, loading: pageLoading, updateProduct } = useProduct(id);

  useEffect(() => {
    if (product) {
      // Parse price to number if it's a string
      const priceValue = typeof product.price === 'string'
        ? parseFloat(product.price.replace('$', '')) || 0
        : product.price;

      // Set images from imageUrls array
      setImageList(product.imageUrls || []);

      form.setFieldsValue({
        product_name: product.name,
        price: priceValue,
        description: product.description,
        ingredients: product.ingredients,
        benefits: product.benefits,
        usage: product.usage,
        instructions: product.instructions,
        targetUser: product.targetUser,
        purpose: product.purpose,
        notes: product.notes,
        otherInfo: product.otherInfo,
      });
      setFaqs(product.faqs || []);
    }
  }, [product, form]);

  const handleSubmit = async (values: any) => {
    if (!id) return;

    setLoading(true);
    try {
      const productData: ProductFormData = {
        ...values,
        price: values.price, // Keep as number or string as per API
        images: imageList, // Use array format for API
        faqs: faqs
      };

      const result = await updateProduct(productData);
      if (result) {
        navigate('/products');
      }
    } catch (error) {
      message.error('Không thể cập nhật sản phẩm');
      console.error('Error updating product:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddFaq = () => {
    faqForm.validateFields().then((values) => {
      if (editingFaqIndex !== null) {
        // Edit existing FAQ
        const newFaqs = [...faqs];
        newFaqs[editingFaqIndex] = values;
        setFaqs(newFaqs);
        message.success('Cập nhật FAQ thành công');
      } else {
        // Add new FAQ
        setFaqs([...faqs, values]);
        message.success('Thêm FAQ thành công');
      }
      
      faqForm.resetFields();
      setFaqModalVisible(false);
      setEditingFaqIndex(null);
    });
  };

  const handleEditFaq = (index: number) => {
    const faq = faqs[index];
    faqForm.setFieldsValue(faq);
    setEditingFaqIndex(index);
    setFaqModalVisible(true);
  };

  const handleDeleteFaq = (index: number) => {
    const newFaqs = faqs.filter((_, i) => i !== index);
    setFaqs(newFaqs);
    message.success('Xóa FAQ thành công');
  };

  const handleCloseFaqModal = () => {
    setFaqModalVisible(false);
    setEditingFaqIndex(null);
    faqForm.resetFields();
  };



  if (pageLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>Đang tải thông tin sản phẩm...</div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/products')}
          style={{ marginBottom: 16 }}
        >
          Quay lại danh sách
        </Button>
        <Title level={2}>Chỉnh sửa sản phẩm</Title>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        scrollToFirstError
      >
        <Row gutter={24}>
          <Col span={24}>
            <Card title="Thông tin cơ bản" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="product_name"
                    label="Tên sản phẩm"
                    rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
                  >
                    <Input placeholder="Nhập tên sản phẩm" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="price"
                    label="Giá ($)"
                    rules={[{ required: true, message: 'Vui lòng nhập giá sản phẩm' }]}
                  >
                    <InputNumber
                      placeholder="Ví dụ: 20.99"
                      min={0}
                      precision={2}
                      step={0.01}
                      style={{ width: '100%' }}
                      formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="notes"
                label="Mô tả sản phẩm"
                rules={[{ required: true, message: 'Vui lòng nhập mô tả sản phẩm' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Nhập mô tả chi tiết về sản phẩm"
                />
              </Form.Item>

              <Form.Item
                label="Hình ảnh sản phẩm"
                required
              >
                <ImageUpload
                  value={imageList}
                  onChange={setImageList}
                  maxCount={5}
                  folder="products"
                />
              </Form.Item>
            </Card>
          </Col>

          <Col span={24}>
            <Card title="Thông tin chi tiết" style={{ marginBottom: 24 }}>
              <Form.Item
                name="ingredients"
                label="Thành phần"
                rules={[{ required: true, message: 'Vui lòng nhập thành phần' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập các thành phần của sản phẩm"
                />
              </Form.Item>

              <Form.Item
                name="benefits"
                label="Lợi ích"
                rules={[{ required: true, message: 'Vui lòng nhập lợi ích' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Nhập các lợi ích của sản phẩm"
                />
              </Form.Item>

              <Form.Item
                name="usage"
                label="Cách sử dụng"
                rules={[{ required: true, message: 'Vui lòng nhập cách sử dụng' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Nhập hướng dẫn sử dụng sản phẩm"
                />
              </Form.Item>

              <Form.Item
                name="instructions"
                label="Hướng dẫn bảo quản"
                rules={[{ required: true, message: 'Vui lòng nhập hướng dẫn bảo quản' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập hướng dẫn bảo quản sản phẩm"
                />
              </Form.Item>
            </Card>
          </Col>

          <Col span={24}>
            <Card title="Thông tin bổ sung" style={{ marginBottom: 24 }}>
              <Form.Item
                name="targetUser"
                label="Đối tượng sử dụng"
                rules={[{ required: true, message: 'Vui lòng nhập đối tượng sử dụng' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập đối tượng phù hợp sử dụng sản phẩm"
                />
              </Form.Item>

              <Form.Item
                name="purpose"
                label="Mục đích sử dụng"
                rules={[{ required: true, message: 'Vui lòng nhập mục đích sử dụng' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập mục đích sử dụng sản phẩm"
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="description"
                    label="Mô tả ngắn"
                    rules={[{ required: true, message: 'Vui lòng nhập mô tả ngắn' }]}
                  >
                    <TextArea
                      rows={3}
                      placeholder="Nhập mô tả ngắn gọn"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="otherInfo"
                    label="Thông tin khác"
                    rules={[{ required: true, message: 'Vui lòng nhập thông tin khác' }]}
                  >
                    <TextArea
                      rows={3}
                      placeholder="Nhập thông tin bổ sung khác"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          <Col span={24}>
            <Card 
              title="Câu hỏi thường gặp (FAQ)"
              extra={
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setFaqModalVisible(true)}
                >
                  Thêm FAQ
                </Button>
              }
              style={{ marginBottom: 24 }}
            >
              {faqs.length === 0 ? (
                <div style={{ textAlign: 'center', color: '#999', padding: '20px 0' }}>
                  Chưa có câu hỏi nào. Nhấn "Thêm FAQ" để thêm câu hỏi.
                </div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                  {faqs.map((item, index) => (
                    <Card
                      key={index}
                      size="small"
                      style={{ border: '1px solid #f0f0f0' }}
                      actions={[
                        <Button
                          key="edit"
                          type="text"
                          onClick={() => handleEditFaq(index)}
                        >
                          Sửa
                        </Button>,
                        <Button
                          key="delete"
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteFaq(index)}
                        >
                          Xóa
                        </Button>
                      ]}
                    >
                      <div>
                        <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#1890ff' }}>
                          Q: {item.question}
                        </div>
                        <div style={{ color: '#666' }}>
                          A: {item.answer}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </Card>
          </Col>
        </Row>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space size="large">
            <Button size="large" onClick={() => navigate('/products')}>
              Hủy
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
            >
              Cập nhật sản phẩm
            </Button>
          </Space>
        </div>
      </Form>

      {/* FAQ Modal */}
      <Modal
        title={editingFaqIndex !== null ? "Chỉnh sửa FAQ" : "Thêm câu hỏi thường gặp"}
        open={faqModalVisible}
        onOk={handleAddFaq}
        onCancel={handleCloseFaqModal}
        okText={editingFaqIndex !== null ? "Cập nhật" : "Thêm"}
        cancelText="Hủy"
      >
        <Form form={faqForm} layout="vertical">
          <Form.Item
            name="question"
            label="Câu hỏi"
            rules={[{ required: true, message: 'Vui lòng nhập câu hỏi' }]}
          >
            <Input placeholder="Nhập câu hỏi" />
          </Form.Item>
          <Form.Item
            name="answer"
            label="Câu trả lời"
            rules={[{ required: true, message: 'Vui lòng nhập câu trả lời' }]}
          >
            <TextArea rows={4} placeholder="Nhập câu trả lời" />
          </Form.Item>
        </Form>
      </Modal>


    </div>
  );
};

export default ProductEdit;
