import React, { useState } from 'react';
import {
  Upload,
  Button,
  Image,
  Progress,
  message,
  Space,
  Card,
  Typography,
  Popconfirm
} from 'antd';
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  LoadingOutlined,
  CloudUploadOutlined
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { cloudinaryService } from '../../services/cloudinaryService';

const { Text } = Typography;

interface ImageUploadProps {
  value?: string[];
  onChange?: (urls: string[]) => void;
  maxCount?: number;
  folder?: string;
  disabled?: boolean;
  showPreview?: boolean;
}

interface UploadingFile {
  uid: string;
  name: string;
  progress: number;
  status: 'uploading' | 'done' | 'error';
  url?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value = [],
  onChange,
  maxCount = 5,
  folder = 'products',
  disabled = false,
  showPreview = true,
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  // Helper function to convert file to base64
  const convertToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const handleUpload = async (file: File): Promise<boolean> => {
    if (!cloudinaryService.isConfigured()) {
      message.error('Cloudinary chưa được cấu hình. Vui lòng liên hệ admin.');
      console.error('Cloudinary configuration missing. Please check VITE_CLOUDINARY_URL environment variable.');
      return false;
    }

    const uid = `${Date.now()}-${Math.random()}`;
    const uploadingFile: UploadingFile = {
      uid,
      name: file.name,
      progress: 0,
      status: 'uploading',
    };

    setUploadingFiles(prev => [...prev, uploadingFile]);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadingFiles(prev =>
          prev.map(f =>
            f.uid === uid && f.progress < 90
              ? { ...f, progress: f.progress + 10 }
              : f
          )
        );
      }, 200);

      const result = await cloudinaryService.uploadImage(file, {
        folder,
        tags: ['product', 'frontend-upload'],
      });

      clearInterval(progressInterval);

      // Update uploading file to completed
      setUploadingFiles(prev =>
        prev.map(f =>
          f.uid === uid
            ? { ...f, progress: 100, status: 'done', url: result.secure_url }
            : f
        )
      );

      // Add to value and call onChange
      const newUrls = [...value, result.secure_url];
      onChange?.(newUrls);

      // Remove from uploading files after a short delay
      setTimeout(() => {
        setUploadingFiles(prev => prev.filter(f => f.uid !== uid));
      }, 1000);

      message.success(`Tải lên thành công: ${file.name}`);
      return true;
    } catch (error) {
      console.error('Upload error:', error);

      // Fallback to base64 if Cloudinary fails
      try {
        console.log('Cloudinary upload failed, falling back to base64...');
        const base64Url = await convertToBase64(file);

        setUploadingFiles(prev =>
          prev.map(f =>
            f.uid === uid
              ? { ...f, progress: 100, status: 'done', url: base64Url }
              : f
          )
        );

        // Add to value and call onChange
        const newUrls = [...value, base64Url];
        onChange?.(newUrls);

        // Remove from uploading files after a short delay
        setTimeout(() => {
          setUploadingFiles(prev => prev.filter(f => f.uid !== uid));
        }, 1000);

        message.warning(`Tải lên thành công (chế độ offline): ${file.name}`);
        return true;
      } catch (fallbackError) {
        console.error('Base64 fallback also failed:', fallbackError);

        setUploadingFiles(prev =>
          prev.map(f =>
            f.uid === uid
              ? { ...f, status: 'error', progress: 0 }
              : f
          )
        );

        message.error(`Tải lên thất bại: ${file.name}`);

        // Remove failed upload after delay
        setTimeout(() => {
          setUploadingFiles(prev => prev.filter(f => f.uid !== uid));
        }, 3000);

        return false;
      }
    }
  };

  const handleRemove = (url: string) => {
    const newUrls = value.filter(u => u !== url);
    onChange?.(newUrls);
    message.success('Đã xóa hình ảnh');
  };

  const handlePreview = (url: string) => {
    setPreviewImage(url);
    setPreviewVisible(true);
  };

  const uploadProps: UploadProps = {
    beforeUpload: (file) => {
      // Validate file type
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('Chỉ có thể tải lên file hình ảnh!');
        return false;
      }

      // Validate file size (max 5MB)
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('Hình ảnh phải nhỏ hơn 5MB!');
        return false;
      }

      // Check max count
      if (value.length + uploadingFiles.length >= maxCount) {
        message.error(`Chỉ có thể tải lên tối đa ${maxCount} hình ảnh!`);
        return false;
      }

      handleUpload(file);
      return false; // Prevent default upload
    },
    showUploadList: false,
    disabled,
  };

  return (
    <div>
      {/* Upload Button */}
      <Upload {...uploadProps}>
        <Button
          icon={<CloudUploadOutlined />}
          disabled={disabled || value.length + uploadingFiles.length >= maxCount}
          size="large"
        >
          Tải lên hình ảnh
        </Button>
      </Upload>

      <div style={{ marginTop: 8 }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          Hỗ trợ: JPG, PNG, GIF. Tối đa {maxCount} hình. Kích thước tối đa: 5MB
        </Text>
      </div>

      {/* Uploading Progress */}
      {uploadingFiles.length > 0 && (
        <div style={{ marginTop: 16 }}>
          {uploadingFiles.map(file => (
            <Card key={file.uid} size="small" style={{ marginBottom: 8 }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <LoadingOutlined />
                <div style={{ flex: 1 }}>
                  <div>{file.name}</div>
                  <Progress
                    percent={file.progress}
                    size="small"
                    status={file.status === 'error' ? 'exception' : 'active'}
                  />
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Image List */}
      {value.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))', gap: 8 }}>
            {value.map((url, index) => (
              <Card
                key={url}
                size="small"
                cover={
                  <Image
                    src={url}
                    alt={`Product image ${index + 1}`}
                    style={{ height: 100, objectFit: 'cover' }}
                    preview={false}
                  />
                }
                actions={[
                  showPreview && (
                    <EyeOutlined
                      key="preview"
                      onClick={() => handlePreview(url)}
                    />
                  ),
                  <Popconfirm
                    key="delete"
                    title="Xác nhận xóa hình ảnh này?"
                    onConfirm={() => handleRemove(url)}
                    okText="Xóa"
                    cancelText="Hủy"
                  >
                    <DeleteOutlined />
                  </Popconfirm>,
                ].filter(Boolean)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {previewImage && (
        <Image
          style={{ display: 'none' }}
          src={previewImage}
          preview={{
            open: previewVisible,
            onOpenChange: setPreviewVisible,
          }}
        />
      )}
    </div>
  );
};

export default ImageUpload;
