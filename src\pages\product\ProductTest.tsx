import React, { useEffect } from 'react';
import { Button, Card, Typography, Spin } from 'antd';
import { useProducts } from '../../hooks/useProducts';

const { Title } = Typography;

const ProductTest: React.FC = () => {
  const {
    products,
    loading,
    pagination,
    loadProducts,
  } = useProducts();

  // Load products when component mounts
  useEffect(() => {
    console.log('ProductTest component mounted, loading products...');
    loadProducts({ page: 1, limit: 5 });
  }, []); // Empty dependency array to run only once

  const handleRefresh = () => {
    console.log('Manual refresh triggered');
    loadProducts({ page: 1, limit: 5 });
  };

  return (
    <div style={{ padding: 24 }}>
      <Card>
        <Title level={3}>Product API Test</Title>
        
        <div style={{ marginBottom: 16 }}>
          <Button onClick={handleRefresh} loading={loading}>
            Refresh Products
          </Button>
        </div>

        {loading && <Spin size="large" />}
        
        <div>
          <p><strong>Products Count:</strong> {products.length}</p>
          <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
          <p><strong>Pagination:</strong> {pagination ? JSON.stringify(pagination) : 'None'}</p>
        </div>

        <div style={{ marginTop: 16 }}>
          <Title level={4}>Products:</Title>
          {products.map((product, index) => (
            <div key={product.id || index} style={{ marginBottom: 8, padding: 8, border: '1px solid #d9d9d9' }}>
              <p><strong>Name:</strong> {(product as any).name || (product as any).product_name}</p>
              <p><strong>Price:</strong> {product.price}</p>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default ProductTest;
