import { useState, useCallback } from 'react';
import { message } from 'antd';
import { productServiceInstance } from '../services/productServiceFactory';
import type {
  Product,
  ProductFormData,
  ProductListParams
} from '../types/product';

interface UseProductsState {
  products: Product[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  } | null;
}

interface UseProductsReturn extends UseProductsState {
  loadProducts: (params?: ProductListParams) => Promise<void>;
  searchProducts: (query: string, params?: Omit<ProductListParams, 'search'>) => Promise<void>;
  createProduct: (productData: ProductFormData) => Promise<Product | null>;
  updateProduct: (id: string | number, productData: ProductFormData) => Promise<Product | null>;
  deleteProduct: (id: string | number) => Promise<boolean>;
  refresh: () => Promise<void>;
}

export function useProducts(): UseProductsReturn {
  const [state, setState] = useState<UseProductsState>({
    products: [],
    loading: false,
    error: null,
    pagination: null,
  });

  const [currentParams, setCurrentParams] = useState<ProductListParams>({});

  const loadProducts = useCallback(async (params?: ProductListParams) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await productServiceInstance.getAllProducts(params);

      // Handle both old format (Product[]) and new format (ProductListResponse)
      if (Array.isArray(response)) {
        // Old format - mock service
        setState(prev => ({
          ...prev,
          products: response,
          pagination: {
            page: params?.page || 1,
            limit: params?.limit || 10,
            total: response.length,
            pages: Math.ceil(response.length / (params?.limit || 10)),
          },
          loading: false,
        }));
      } else {
        // New format - API service
        if (response.success) {
          setState(prev => ({
            ...prev,
            products: response.data,
            pagination: response.pagination,
            loading: false,
          }));
        } else {
          throw new Error('Failed to load products');
        }
      }
      setCurrentParams(params || {});
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load products';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      message.error(errorMessage);
    }
  }, []);

  const searchProducts = useCallback(async (query: string, params?: Omit<ProductListParams, 'search'>) => {
    await loadProducts({ ...params, search: query });
  }, [loadProducts]);

  const createProduct = useCallback(async (productData: ProductFormData): Promise<Product | null> => {
    try {
      const result = await productServiceInstance.createProduct(productData);
      message.success('Tạo sản phẩm thành công');
      // Refresh the product list
      await loadProducts(currentParams);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create product';
      message.error(errorMessage);
      return null;
    }
  }, [currentParams]); // Remove loadProducts from dependencies

  const updateProduct = useCallback(async (id: string | number, productData: ProductFormData): Promise<Product | null> => {
    try {
      const result = await productServiceInstance.updateProduct(id, productData);
      message.success('Cập nhật sản phẩm thành công');
      // Refresh the product list
      await loadProducts(currentParams);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update product';
      message.error(errorMessage);
      return null;
    }
  }, [currentParams]); // Remove loadProducts from dependencies

  const deleteProduct = useCallback(async (id: string | number): Promise<boolean> => {
    try {
      const result = await productServiceInstance.deleteProduct(id);
      if (result) {
        message.success('Xóa sản phẩm thành công');
        // Refresh the product list
        await loadProducts(currentParams);
      }
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete product';
      message.error(errorMessage);
      return false;
    }
  }, [currentParams]); // Remove loadProducts from dependencies

  const refresh = useCallback(async () => {
    await loadProducts(currentParams);
  }, [currentParams]); // Remove loadProducts from dependencies

  // Products will be loaded explicitly from components

  return {
    ...state,
    loadProducts,
    searchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    refresh,
  };
}
