import type { Product, ProductFormData } from '../types/product';
import productsData from '../data/products_only.json';

// Simulate API calls with localStorage for persistence
const STORAGE_KEY = 'products';

class ProductService {
  private products: Product[] = [];

  constructor() {
    this.loadProducts();
  }

  private loadProducts(): void {
    try {
      const storedProducts = localStorage.getItem(STORAGE_KEY);
      if (storedProducts) {
        this.products = JSON.parse(storedProducts);
      } else {
        // Initialize with data from JSON file and add IDs
        this.products = productsData.map((product, index) => ({
          ...product,
          id: String(product.id ?? `product_${index + 1}`),
          name: product.product_name // Map product_name to name for Product type compatibility
        }));
        this.saveProducts();
      }
    } catch (error) {
      console.error('Error loading products:', error);
      this.products = [];
    }
  }

  private saveProducts(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.products));
    } catch (error) {
      console.error('Error saving products:', error);
    }
  }

  // Get all products
  async getAllProducts(): Promise<Product[]> {
    return new Promise((resolve) => {
      setTimeout(() => resolve([...this.products]), 100);
    });
  }

  // Get product by ID
  async getProductById(id: string): Promise<Product | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const product = this.products.find(p => String(p.id) === id);
        resolve(product || null);
      }, 100);
    });
  }

  // Create new product
  async createProduct(productData: ProductFormData): Promise<Product> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const newProduct: Product = {
            ...productData,
            id: `product_${Date.now()}`
          };
          this.products.push(newProduct);
          this.saveProducts();
          resolve(newProduct);
        } catch (error) {
          reject(error);
        }
      }, 100);
    });
  }

  // Update product
  async updateProduct(id: string, productData: ProductFormData): Promise<Product> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const index = this.products.findIndex(p => p.id === id);
          if (index === -1) {
            reject(new Error('Product not found'));
            return;
          }
          
          const updatedProduct: Product = {
            ...productData,
            id
          };
          
          this.products[index] = updatedProduct;
          this.saveProducts();
          resolve(updatedProduct);
        } catch (error) {
          reject(error);
        }
      }, 100);
    });
  }

  // Delete product
  async deleteProduct(id: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const index = this.products.findIndex(p => p.id === id);
          if (index === -1) {
            reject(new Error('Product not found'));
            return;
          }
          
          this.products.splice(index, 1);
          this.saveProducts();
          resolve(true);
        } catch (error) {
          reject(error);
        }
      }, 100);
    });
  }

  // Search products
  async searchProducts(query: string): Promise<Product[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const lowercaseQuery = query.toLowerCase();
        const filteredProducts = this.products.filter(product =>
          product.product_name.toLowerCase().includes(lowercaseQuery) ||
          product.notes.toLowerCase().includes(lowercaseQuery) ||
          product.ingredients.toLowerCase().includes(lowercaseQuery)
        );
        resolve(filteredProducts);
      }, 100);
    });
  }
}

export const productService = new ProductService();
export default productService;
