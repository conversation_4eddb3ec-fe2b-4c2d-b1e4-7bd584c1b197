export interface FAQ {
  id?: number;
  productId?: number;
  question: string;
  answer: string;
}

export interface Product {
  id?: number;
  name: string;
  price: number | string;
  imageUrls?: string[];
  ingredients: string;
  instructions: string;
  benefits: string;
  usage: string;
  notes?: string;
  otherInfo?: string;
  targetUser?: string;
  purpose?: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
  faqs?: FAQ[];
}

export interface ProductFormData {
  product_name: string;
  price: number | string;
  images?: string[];
  ingredients: string;
  instructions: string;
  benefits: string;
  usage: string;
  notes?: string;
  otherInfo?: string;
  targetUser?: string;
  purpose?: string;
  description?: string;
  faqs?: FAQ[];
}

export interface ProductListParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ProductListResponse {
  success: boolean;
  data: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface BulkImportRequest {
  products: ProductFormData[];
}

export interface BulkImportResponse {
  success: boolean;
  message: string;
  data: {
    created: Product[];
    errors: any[];
  };
}
